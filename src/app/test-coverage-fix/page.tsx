"use client";

import { GroupedCoverageDisplay } from '@/components/shared/PolicyDetailsDrawer';
import { PolicyCoverage } from '@/types/policy';
import { GuaranteeType } from '@prisma/client';

const testCoverages: PolicyCoverage[] = [
  {
    title: 'Responsabilidad Civil Obligatoria',
    limit: null,
    description: 'Responsabilidad civil obligatoria',
    guaranteeType: GuaranteeType.MANDATORY_LIABILITY,
    id: '1',
    customName: null,
    limitIsUnlimited: false,
    limitIsFullCost: false,
    limitPerDay: null,
    limitMaxDays: null,
    limitMaxMonths: null,
    liabilityBodilyCap: 70000000,
    liabilityPropertyCap: 15000000,
    deductible: null,
    deductiblePercent: null,
  },
  {
    title: 'Defensa Jurídica',
    limit: null,
    description: 'Defensa jurídica ilimitada',
    guaranteeType: GuaranteeType.LEGAL_DEFENSE,
    id: '2',
    customName: null,
    limitIsUnlimited: true,
    limitIsFullCost: false,
    limitPerDay: null,
    limitMaxDays: null,
    limitMaxMonths: null,
    liabilityBodilyCap: null,
    liabilityPropertyCap: null,
    deductible: null,
    deductiblePercent: null,
  },
  {
    title: 'Daños del Vehículo',
    limit: 50000,
    description: 'Daños propios del vehículo',
    guaranteeType: GuaranteeType.VEHICLE_DAMAGE,
    id: '3',
    customName: null,
    limitIsUnlimited: false,
    limitIsFullCost: false,
    limitPerDay: null,
    limitMaxDays: null,
    limitMaxMonths: null,
    liabilityBodilyCap: null,
    liabilityPropertyCap: null,
    deductible: 500,
    deductiblePercent: null,
  },
  {
    title: 'Vehículo de Sustitución',
    limit: null,
    description: 'Vehículo de sustitución por accidente',
    guaranteeType: GuaranteeType.VEHICLE_REPLACEMENT,
    id: '4',
    customName: null,
    limitIsUnlimited: false,
    limitIsFullCost: false,
    limitPerDay: 80,
    limitMaxDays: 10,
    limitMaxMonths: null,
    liabilityBodilyCap: null,
    liabilityPropertyCap: null,
    deductible: null,
    deductiblePercent: null,
  },
  {
    title: 'Asistencia en Viaje',
    limit: null,
    description: 'Remolque del vehículo',
    guaranteeType: GuaranteeType.TRAVEL_ASSISTANCE,
    id: '5',
    customName: null,
    limitIsUnlimited: false,
    limitIsFullCost: true,
    limitPerDay: null,
    limitMaxDays: null,
    limitMaxMonths: null,
    liabilityBodilyCap: null,
    liabilityPropertyCap: null,
    deductible: null,
    deductiblePercent: null,
  },
];

"use client";

import { GroupedCoverageDisplay } from '@/components/shared/PolicyDetailsDrawer';
import { PolicyCoverage } from '@/types/policy';
import { GuaranteeType } from '@prisma/client';

const testCoverages: PolicyCoverage[] = [
  {
    title: 'Responsabilidad Civil Obligatoria',
    limit: null,
    description: 'Responsabilidad civil obligatoria',
    guaranteeType: GuaranteeType.MANDATORY_LIABILITY,
    liabilityBodilyCap: 70000000,
    liabilityPropertyCap: 15000000,
  },
  {
    title: 'Defensa Jurídica',
    limit: null,
    description: 'Defensa jurídica ilimitada',
    guaranteeType: GuaranteeType.LEGAL_DEFENSE,
    limitIsUnlimited: true,
  },
  {
    title: 'Daños del Vehículo',
    limit: 50000,
    description: 'Daños propios del vehículo',
    guaranteeType: GuaranteeType.VEHICLE_DAMAGE,
    deductible: 500,
  },
];

export default function TestCoverageFixPage() {
  // Test the grouping function directly
  const testGrouping = () => {
    console.log('🧪 Testing coverage grouping with test data...');
    console.log('Test coverages:', testCoverages);
  };

  return (
    <div className="container mx-auto p-8">
      <h1 className="text-2xl font-bold mb-6">Coverage Grouping Fix Test</h1>

      <div className="mb-4">
        <button
          onClick={testGrouping}
          className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
        >
          Test Grouping Function
        </button>
      </div>

      <div className="bg-white rounded-lg border p-6">
        <GroupedCoverageDisplay coverages={testCoverages} />
      </div>
    </div>
  );
}
  return (
    <div className="container mx-auto p-8 space-y-8">
      <h1 className="text-3xl font-bold mb-8">Coverage Grouping Fix Test</h1>
      
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
        <h2 className="text-lg font-semibold text-yellow-800 mb-2">Test Purpose</h2>
        <p className="text-yellow-700">
          This page tests the fix for the coverage grouping issue where coverages were showing 
          "Total: X coberturas en 0 categorías" instead of being properly grouped.
        </p>
      </div>

      <section>
        <h2 className="text-2xl font-semibold mb-4">Grouped Coverage Display Test</h2>
        <div className="bg-white rounded-lg border p-6">
          <GroupedCoverageDisplay coverages={testCoverages} />
        </div>
      </section>

      <section>
        <h2 className="text-2xl font-semibold mb-4">Expected Results</h2>
        <div className="bg-gray-50 rounded-lg p-6">
          <ul className="space-y-2 text-sm">
            <li>✅ Should show "Total: 5 coberturas en X categorías" (where X > 0)</li>
            <li>✅ Should group coverages into proper categories:</li>
            <ul className="ml-4 space-y-1">
              <li>• Civil Liability: Responsabilidad Civil Obligatoria</li>
              <li>• Legal Defense & Management: Defensa Jurídica</li>
              <li>• Damage to Insured Vehicle: Daños del Vehículo</li>
              <li>• Replacement Vehicle: Vehículo de Sustitución</li>
              <li>• Travel Assistance: Asistencia en Viaje</li>
            </ul>
            <li>✅ Each coverage should display with proper limit formatting</li>
            <li>✅ Should show liability caps, unlimited flags, per-day limits, etc.</li>
          </ul>
        </div>
      </section>

      <section>
        <h2 className="text-2xl font-semibold mb-4">Debug Information</h2>
        <div className="bg-gray-100 rounded-lg p-4">
          <pre className="text-xs overflow-auto">
            {JSON.stringify(testCoverages, null, 2)}
          </pre>
        </div>
      </section>
    </div>
  );
}
